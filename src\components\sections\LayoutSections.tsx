import React from "react";
import Container from "../Container";
import ThemeToggle from "../ThemeToggle";

// Header Navigation Component
export function HeaderNav() {
  return (
    <header className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-white/60 bg-white/80 dark:bg-zinc-950/60 border-b">
      <Container className="h-14 flex items-center justify-between">
        <div className="font-heading font-semibold">Brand</div>
        <nav className="hidden sm:flex items-center gap-6 text-sm">
          <a href="#benefits" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Benefits</a>
          <a href="#location" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Lokasi</a>
          <a href="#bestsellers" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Best Sellers</a>
          <a href="#testimonials" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Testimoni</a>
          <a href="#about" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Tentang Kami</a>
          <a href="#gallery" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">Galeri</a>
          <a href="#faq" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded">FAQ</a>
        </nav>
        <div className="flex items-center gap-3">
          <ThemeToggle />
          <a href="#contact" className="inline-flex h-9 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-4 text-sm hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg">
            Contact
          </a>
        </div>
      </Container>
    </header>
  );
}

// Site Footer Component
export function SiteFooter() {
  return (
    <footer className="border-t py-10 mt-10">
      <Container className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-muted-foreground-light">© {new Date().getFullYear()} Brand. All rights reserved.</div>
        <nav className="flex items-center gap-4 text-sm">
          <a className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded" href="#">Privacy</a>
          <a className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded" href="#">Terms</a>
          <a className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded" href="#contact">Contact</a>
        </nav>
      </Container>
    </footer>
  );
}
