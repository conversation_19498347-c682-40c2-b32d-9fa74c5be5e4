import { <PERSON><PERSON><PERSON><PERSON>, SiteFooter } from "@/components/sections/LayoutSections";
import { Hero, KeyBenefits, About, LocationHours } from "@/components/sections/ContentSections";
import { BestSellers, Testimonials, Gallery, FAQ } from "@/components/sections/InteractiveSections";
import { Contact } from "@/components/sections/ContactSection";

export default function Home() {
  return (
    <div>
      <HeaderNav />
      <main>
        <section id="hero"><Hero /></section>
        <section id="benefits"><KeyBenefits /></section>
        <section id="location"><LocationHours /></section>
        <section id="bestsellers"><BestSellers /></section>
        <section id="testimonials"><Testimonials /></section>
        <section id="about"><About /></section>
        <section id="gallery"><Gallery /></section>
        <section id="faq"><FAQ /></section>
        <section id="contact"><Contact /></section>
      </main>
      <SiteFooter />
    </div>
  );
}
