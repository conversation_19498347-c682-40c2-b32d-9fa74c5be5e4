import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Landing Template",
  description: "Landing page template built with Next.js and Tailwind CSS",
  metadataBase: new URL('https://example.com'),
  openGraph: {
    title: "Landing Template",
    description: "Landing page template built with Next.js and Tailwind CSS",
    url: '/',
    siteName: 'Landing Template',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Landing Template",
    description: "Landing page template built with Next.js and Tailwind CSS",
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  alternates: {
    canonical: '/',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(){
                try {
                  var s = localStorage.getItem('theme');
                  var m = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                  var t = s || m;
                  var r = document.documentElement;
                  r.classList.remove('light', 'dark');
                  r.classList.add(t);
                } catch(e){}
              })();
            `,
          }}
        />
      </head>
      <body className={`${inter.variable} ${poppins.variable} antialiased`}>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
